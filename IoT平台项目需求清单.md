# IoT平台项目需求清单

## 项目概述

### 项目名称
智能IoT设备管理平台开发项目

### 项目背景
本项目旨在开发一套完整的IoT设备管理平台，包含Insight应用系统和IoT后台管理平台，为智能设备提供全生命周期管理服务。

### 项目目标
- 构建统一的IoT设备管理平台
- 实现设备远程监控和管理
- 提供数据可视化和分析功能
- 支持多语言国际化应用

## 技术架构要求

### 前端技术栈
- **Insight应用Web端**：现代化Web前端框架
- **IoT管理平台Web端**：响应式管理界面
- 支持多语言国际化
- 移动端适配

### 后端技术栈
- **Insight Java后端**：微服务架构
- **IoT平台Java后端**：分布式系统架构
- RESTful API设计
- RPC远程调用
- WebSocket实时通信

### 数据存储
- 关系型数据库
- 时序数据库
- 文件存储服务
- 缓存系统

## 功能需求清单

### 一、系统设计模块

| 序号 | 功能包名 | 一级功能 | 二级功能 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 系统设计 | 需求调研分析 | 需求调研 | 深入分析业务需求，制定QA流程 |
| 2 | 系统设计 | 架构设计 | 架构设计 | 系统架构设计，流程结构设计 |
| 3 | 系统设计 | 核心功能设计 | 核心设计 | 关键业务流程，核心功能设计 |
| 4 | 系统设计 | 详细设计 | 详细设计 | 详细设计及关键技术图表、协议制定 |
| 5 | 系统设计 | 界面设计 | UI设计 | UI/UX设计，界面原型设计 |

### 二、Insight应用系统

| 序号 | 功能包名 | 一级功能 | 二级功能 | 功能描述 |
|------|----------|----------|----------|----------|
| 6 | Insight应用 | 用户登录 | Dealer商户登录Insight平台 | Dealer通过邮箱或手机号登录Insight平台，并实现经销商列表、启用、停用、编辑经销商信息接口等 |
| 7 | Insight应用 | 用户登录 | 密码输入、邮箱验证码 | 用户密码输入、邮箱验证码验证，密码强度验证 |
| 8 | Insight应用 | 用户登录 | 用户协议、隐私协议 | 查看用户协议、隐私协议等，协议支持国际化 |
| 9 | Insight应用 | 用户登录 | 用户角色权限管理 | 用户后台资源权限管理系统功能的控制 |
| 10 | Insight应用 | 账户管理 | 账户信息、编辑个人信息 | 展示、编辑账户信息 |
| 11 | Insight应用 | 账户管理 | 用户修改密码、忘记密码重置 | 用户修改密码、忘记密码重置功能 |
| 12 | Insight应用 | 设备列表 | 设备添加、编辑、设备移除 | 支持经销商通过设备序列号、同意用户隐私协议添加新的robot设备，编辑设备名称、移除设备，后台提供设备信息查询 |
| 13 | Insight应用 | 设备列表 | 设备列表筛选、基础信息、状态信息 | 显示已添加的robot设备列表，支持按名称、SN查询，支持按Model、设备状态筛选设备 |
| 14 | Insight应用 | 设备列表 | 设备运行状态、连接展示 | 设备运行状态、连接展示 |
| 15 | Insight应用 | 消息通知 | 故障警报信息列表、详情展示 | 接收实时robot设备故障警报信息，查看消息详情，故障报警，故障发生原因、解决措施等，后台故障警报数据，设备当前运行状态显示等 |
| 16 | Insight应用 | 消息通知 | 消息是否已读、未读、已读、删除列表、已读删除 | 消息已读/未读、已读颜色区别显示，按故障发生显示故障发生时间，时间格式：年/月/日，小时/分钟；按解决显示解决时间，时间格式：年/月/日，小时/分钟。同时显示故障持续时间：结束时间-开始时间； |
| 17 | Insight应用 | 消息通知 | 消息推送实时事件 | 通过后台数据转换消息，然后通过websocket通知insight平台，实时实现 |

### 三、Insight设备管理

| 序号 | 功能包名 | 一级功能 | 二级功能 | 功能描述 |
|------|----------|----------|----------|----------|
| 18 | Insight设备管理 | Dashboard | 设备基础信息 | 显示设备图片, model number, SN, deviceID |
| 19 | Insight设备管理 | Dashboard | 设备固件版本信息 | 显示设备当前固件版本，推动最新固件版本，比对是否为最新 |
| 20 | Insight设备管理 | Dashboard | 设备地图展示/地图统计 | 展示设备安装地图，包含各种元素（边界、障碍、通道、基站、充电桩等），地图上标注结构化存储地图数据，地图数据api接口提供 |
| 21 | Insight设备管理 | Dashboard | 地图统计信息展示 | 地图统计信息展示，地图总面积、地图总数信息，各元素类型（各种元素数量/通道数量等） |
| 22 | Insight设备管理 | Dashboard | 设备使用统计 | 展示总使用时长、总充电时长、设备碰撞和使用距离，总使用时长 |
| 23 | Insight设备管理 | Dashboard | 设备故障信息 | 展示设备当前是否故障状态、故障代码、故障发生时间、故障持续时间，通过后台Insight数据津贴，故障信息维护获取等 |
| 24 | Insight设备管理 | Dashboard | 设备维护信息 | 展示各种维护状态、维护项目和可用性，各种模块、刀片消耗和使用时间，刀片使用时间，重置充电的循环/次数 |
| 25 | Insight设备管理 | 地图 | 设备安装地图 | 展示详细的设备地图，包含所有的图元素，支持点击元素的展示详情，地图支持切换卫星图、普通图，缩放大小，重置默认等 |
| 26 | Insight设备管理 | 地图 | 地图信息显示 | 当用户点击地图某一元素时，显示该元素名称、类型图标对应信息 |
| 27 | Insight设备管理 | 地图 | 时间选择 | 默认选择最近7天时间段，可根据用户需要自定义选取时间段，对应相应数据 |
| 28 | Insight设备管理 | 地图 | 割草机轨迹历史 | 在地图上叠加展示设备的轨迹 |
| 29 | Insight设备管理 | 地图 | 故障信息故障地图 | 在地图上叠加展示故障的信息，故障地点，包含故障名称、故障代码、开始/结束时间、故障时长、故障原因和解决措施 |
| 30 | Insight设备管理 | 地图 | GPS信号强度热力图 | 在地图上展示对应时间段内的GPS信号强度分布，支持切换计算方式查看 |
| 31 | Insight设备管理 | 地图 | GPS精度分布热力图 | 在地图上叠加展示设备的GNSS精度分布热力图（最好/差/平均三种） |
| 32 | Insight设备管理 | 地图 | 4G信号强度热力图 | 在地图上叠加展示设备的4G/蜂窝信号强度热力图（最好/差/平均三种） |
| 33 | Insight设备管理 | 地图 | GPS定位精度热力图 | 在地图上叠加展示设备的GNSS定位精度热力图（最好/差/平均三种） |
| 34 | Insight设备管理 | 地图 | 环境传感器温度热力图 | 在地图上展示对应时间段内的环境传感器温度分布，支持切换计算方式查看 |
| 35 | Insight设备管理 | 地图 | 声音传感器热力图 | 在地图上展示对应时间段内的声音传感器分布，支持切换计算方式查看 |
| 36 | Insight设备管理 | 地图 | 陀螺仪传感器热力图 | 在地图上展示对应时间段内的陀螺仪传感器分布，支持切换计算方式查看 |
| 37 | Insight设备管理 | 地图 | 磁场信号强度热力图 | 在地图上展示对应时间段内的磁场信号强度分布，支持切换计算方式查看 |
| 38 | Insight设备管理 | 地图 | RTK定位精度热力图 | 在地图上展示对应时间段内的RTK定位精度分布，支持切换计算方式查看 |
| 39 | Insight设备管理 | 地图 | RTK信号强度热力图 | 在地图上叠加展示设备与附近RTK基站的信号强度热力图（信号强度、信号质量、信号强度差） |
| 40 | Insight设备管理 | 数据分析地方 | 时间选择 | 用户按时间选择控件选择对应时间段，查看对应数据，包含开始时间、是否有故障、地图是否有变化等 |
| 41 | Insight设备管理 | 数据分析地方 | 割草机轨迹历史 | 展示选时间段内割草的地图，支持在地图上回放割草的轨迹历史，轨迹历史对应地图，地图支持展示/隐藏元素，切换卫星图/普通图，缩放/缩小，重置默认 |
| 42 | Insight设备管理 | 数据分析地方 | 割草机轨迹历史地图 | 展示选时间段内割草的地图，支持在地图上回放割草的轨迹历史，轨迹历史对应地图，地图支持展示/隐藏元素，切换卫星图/普通图，缩放/缩小，重置默认 |

### 四、备份管理模块

| 序号 | 功能包名 | 一级功能 | 二级功能 | 功能描述 |
|------|----------|----------|----------|----------|
| 43 | Insight设备管理 | 备份 | 设备备份档案 | 地图备份、配置备份、固件备份、状态备份，默认展示最近7天的列表，包含地图、配置、固件等按选取时间段数据，作为dealer验证消费的档案点"certified clones"，即恢复restore points，可以在后续恢复到某一档案点 |
| 44 | Insight设备管理 | 备份 | 恢复存储点 | 用户可以点击选择某一时间点的档案，包含每天备份的配置文件数据 |
| 45 | Insight设备管理 | 备份 | 恢复存储点 | 用户点击Restore，显示Clone的地图、配置数据和固件版本，确认基站没有被移动，确认进入数据库克隆流程 |
| 46 | Insight设备管理 | 备份 | 恢复备份 | 选取需要恢复的数据档案，对用户配置数据恢复，恢复过程同时应该在机器人割草或用户app正在机器人操作中，也支持将用户配置数据转移到同一型号的其他机器 |
| 47 | Insight设备管理 | 固件版本查看 | 固件版本查看 | 展示云端发布的、适用于该用户型号的固件版本信息，包含发布时间、版本号、发布方式等，同时标注出当前固件版本，查看发布历史 |

### 五、IoT后台管理平台

| 序号 | 功能包名 | 一级功能 | 二级功能 | 功能描述 |
|------|----------|----------|----------|----------|
| 48 | IoT后台平台 | 邮件工具管理 | 邮件工具创建发送通知邮件，邮件模板，邮件国际化资源 | 后台平台创建dealer公司名称、公司、账号、密码等，邮件模板发给目标经销商，邮件模板支持国际化 |
| 49 | IoT后台平台 | 邮件工具管理 | 邮件工具管理列表 | 根据创建时间、名称、状态、内容、id等条件查询邮件列表，启用、停用、编辑邮件信息 |
| 50 | IoT后台平台 | 用户协议管理 | insight平台用户协议、隐私协议创建、列表查询 | 后台平台支持维护Insight平台dealer用户的用户协议、隐私协议维护，支持国际化 |
| 51 | IoT后台平台 | 警报信息 | 后台平台警报配置修改 | 后台配置的故障信息、警报信息，支持推送到insight平台 |
| 52 | IoT后台平台 | 警报信息 | Insight消息推送、消息管理 | 警报信息存储、列表、已读、未读、删除 |
| 53 | IoT后台平台 | OTA升级记录 | 后台平台创建展示OTA升级记录 | 查看设备各种不同升级方式的升级日志记录 |
| 54 | IoT后台平台 | Protobuf数据处理实时数据库 | Protobuf数据处理实时数据库 | Protobuf数据处理方式包含：a) APP数据收到的protobuf数据后转发的方式；b) 小机4G上报的protobuf数据，自动按topic转发数据库的方式 |

## 非功能性需求

### 性能要求
- 系统响应时间 < 2秒
- 支持并发用户数 > 1000
- 数据处理能力 > 10万条/分钟
- 系统可用性 > 99.9%

### 安全要求
- 数据传输加密
- 用户身份认证
- 权限访问控制
- 操作日志审计
- 数据备份恢复

### 兼容性要求
- 支持主流浏览器
- 移动端适配
- 多语言国际化
- 跨平台部署

## 项目交付物

### 交付物清单

| 类别 | 交付物名称 | 数量 | 格式 | 说明 |
|------|------------|------|------|------|
| 软件交付物 | 完整源代码 | 1套 | 压缩包 | 包含前后端完整源码 |
| 软件交付物 | 可执行程序 | 1套 | 安装包 | 可直接部署的程序包 |
| 软件交付物 | 数据库脚本 | 1套 | SQL文件 | 数据库初始化脚本 |
| 软件交付物 | 配置文件 | 1套 | 配置文件 | 系统运行配置 |
| 文档交付物 | 需求规格说明书 | 1份 | Word/PDF | 详细需求文档 |
| 文档交付物 | 系统设计文档 | 1份 | Word/PDF | 架构和设计文档 |
| 文档交付物 | 接口文档 | 1份 | Word/PDF | API接口说明 |
| 文档交付物 | 用户操作手册 | 1份 | Word/PDF | 用户使用指南 |
| 文档交付物 | 部署运维手册 | 1份 | Word/PDF | 系统部署指南 |
| 文档交付物 | 测试报告 | 1份 | Word/PDF | 完整测试报告 |
| 培训服务 | 系统管理员培训 | 2天 | 现场培训 | 技术人员培训 |
| 培训服务 | 最终用户培训 | 1天 | 现场培训 | 业务人员培训 |
| 培训服务 | 技术支持服务 | 12个月 | 远程支持 | 质保期技术支持 |

## 项目工期安排

### 总体工期
项目总工期：**398.5人天**

### 分模块工期

| 模块名称 | 工期（人天） | 说明 |
|----------|-------------|------|
| 系统设计 | 12 | 需求分析、架构设计、详细设计等 |
| Insight应用Web端 | 169.5 | 前端界面开发 |
| Insight应用Java后端 | 105.5 | 后端服务开发 |
| IoT平台Web端 | 9.5 | 管理平台前端 |
| IoT平台Java后端 | 114 | 管理平台后端 |
| **总计** | **398.5** | **项目总工期** |

### 里程碑节点

| 里程碑 | 节点名称 | 预计完成时间 | 交付物 |
|--------|----------|-------------|--------|
| M1 | 需求确认完成 | 项目启动后2周 | 需求规格说明书 |
| M2 | 系统设计评审通过 | 项目启动后4周 | 系统设计文档 |
| M3 | 核心功能开发完成 | 项目启动后12周 | 核心功能模块 |
| M4 | 系统集成测试完成 | 项目启动后16周 | 测试报告 |
| M5 | 用户验收测试通过 | 项目启动后18周 | 验收报告 |
| M6 | 项目正式上线 | 项目启动后20周 | 完整系统 |

## 验收标准

### 验收标准表

| 验收类别 | 验收项目 | 验收标准 | 验收方法 |
|----------|----------|----------|----------|
| 功能验收 | 所有功能模块正常运行 | 100%功能可用 | 功能测试 |
| 功能验收 | 业务流程完整可用 | 端到端流程通畅 | 业务测试 |
| 功能验收 | 数据处理准确无误 | 数据准确率100% | 数据验证 |
| 功能验收 | 用户界面友好易用 | 用户满意度≥90% | 用户体验测试 |
| 性能验收 | 系统响应时间 | <2秒 | 性能测试 |
| 性能验收 | 并发用户支持 | >1000用户 | 压力测试 |
| 性能验收 | 系统稳定性 | 连续运行72小时无故障 | 稳定性测试 |
| 安全验收 | 安全漏洞扫描 | 无高危漏洞 | 安全扫描 |
| 安全验收 | 数据安全保护 | 符合安全规范 | 安全审计 |
| 安全验收 | 权限控制 | 权限控制有效 | 权限测试 |

## 技术支持与维护

### 质保期
- 免费质保期：12个月
- 7×24小时技术支持
- 远程技术支持服务

### 维护服务
- 系统监控服务
- 定期巡检服务
- 故障应急响应
- 版本升级服务

---

**注：本需求清单基于提供的IoT平台报价单V1.1制定，具体实施过程中可根据实际情况进行调整和完善。**
