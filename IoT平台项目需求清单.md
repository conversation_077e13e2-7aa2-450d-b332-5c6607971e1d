# IoT平台项目需求清单

## 项目概述

### 项目名称
智能IoT设备管理平台开发项目

### 项目背景
本项目旨在开发一套完整的IoT设备管理平台，包含Insight应用系统和IoT后台管理平台，为智能设备提供全生命周期管理服务。

### 项目目标
- 构建统一的IoT设备管理平台
- 实现设备远程监控和管理
- 提供数据可视化和分析功能
- 支持多语言国际化应用

## 技术架构要求

### 前端技术栈
- **Insight应用Web端**：现代化Web前端框架
- **IoT管理平台Web端**：响应式管理界面
- 支持多语言国际化
- 移动端适配

### 后端技术栈
- **Insight Java后端**：微服务架构
- **IoT平台Java后端**：分布式系统架构
- RESTful API设计
- RPC远程调用
- WebSocket实时通信

### 数据存储
- 关系型数据库
- 时序数据库
- 文件存储服务
- 缓存系统

## 功能需求清单

### 一、系统设计模块

#### 1.1 需求分析与设计
- **需求调研**：深入分析业务需求，制定QA流程
- **架构设计**：系统架构设计，流程结构设计
- **核心功能设计**：关键业务流程，核心功能设计
- **详细设计**：详细设计及关键技术图表、协议制定
- **界面设计**：UI/UX设计，界面原型设计

### 二、Insight应用系统

#### 2.1 用户登录模块
- **Dealer商户登录**
  - 支持邮箱、手机号登录
  - 密码重置功能
  - 登录状态管理
  - 多因素认证

- **密码管理**
  - 密码强度验证
  - 邮箱验证码功能
  - 安全问题设置

- **用户协议管理**
  - 用户协议查看
  - 隐私协议管理
  - 多语言支持

- **权限管理**
  - 用户角色权限控制
  - 资源访问控制
  - 操作日志记录

#### 2.2 账户管理模块
- **账户信息管理**
  - 账户详情查看
  - 信息编辑功能
  - 头像上传

- **密码修改**
  - 原密码验证
  - 新密码设置
  - 忘记密码重置

#### 2.3 设备列表模块
- **设备添加管理**
  - 设备序列号添加
  - 隐私协议确认
  - 设备信息编辑
  - 设备移除功能

- **设备列表展示**
  - 设备名称、SN查询
  - Model、设备状态筛选
  - 设备详情查看
  - 分页显示

- **设备状态监控**
  - 实时状态显示
  - 在线/离线状态
  - 工作状态监控

#### 2.4 消息通知模块
- **故障警报信息**
  - 实时故障信息列表
  - 故障详情查看
  - 故障原因分析
  - 解决方案推荐

- **消息状态管理**
  - 已读/未读标记
  - 消息删除功能
  - 消息分类显示

- **实时推送**
  - WebSocket消息推送
  - 消息实时更新
  - 推送状态统计

### 三、Insight设备管理

#### 3.1 Dashboard仪表板
- **设备基础信息**
  - 设备图片展示
  - Model Number、SN、Device ID
  - 固件版本信息
  - 版本更新提醒

- **设备地图统计**
  - 设备安装地图
  - 地图元素展示（边界、障碍、通道、基站、充电桩）
  - 地图结构数据存储
  - 统计信息展示

- **设备使用统计**
  - 使用时长统计
  - 充电时间统计
  - 碰撞次数统计
  - 行驶距离统计

- **设备故障信息**
  - 当前故障状态
  - 故障代码显示
  - 故障发生时间
  - 故障持续时间

- **设备维护信息**
  - 维护状态显示
  - 维护项目和可用性
  - 刀片使用时间
  - 维护提醒功能

#### 3.2 地图功能模块
- **设备安装地图**
  - 详细地图展示
  - 地图元素筛选
  - 地图切换功能
  - 缩放控制

- **地图信息显示**
  - 充电桩信息
  - 基站信息  
  - 边界区域信息
  - 通道信息
  - 禁行区域信息

- **时间选择器**
  - 默认7天时间段
  - 自定义时间范围
  - 时间筛选功能

- **设备轨迹历史**
  - 地图轨迹显示
  - 历史路径回放

- **故障信息地图**
  - 故障位置标记
  - 故障详情显示
  - 故障时间信息

- **信号强度热力图**
  - GPS信号强度分布
  - GPS精度分布图
  - 4G信号强度图
  - GPS定位精度图
  - 环境传感器数据图
  - RTK定位精度图
  - RTK信号强度图

#### 3.3 数据分析模块
- **时间选择功能**
  - 时间范围筛选
  - 数据对比分析

- **割草历史记录**
  - 工作状态记录
  - 工作时长统计
  - 状态筛选功能

- **割草轨迹地图**
  - 轨迹回放功能
  - 地图元素控制
  - 历史数据查询

### 四、备份管理模块

#### 4.1 设备备份档案
- **备份列表管理**
  - 地图备份记录
  - 配置备份记录
  - 固件备份记录
  - 状态备份记录

- **备份创建**
  - 自动备份功能
  - 手动备份创建
  - 备份验证

#### 4.2 恢复存储点
- **恢复前验证**
  - 基站位置验证
  - 设备状态检查
  - 电量检查

#### 4.3 恢复备份
- **备份恢复功能**
  - 数据恢复流程
  - 恢复状态监控
  - 恢复历史记录

#### 4.4 固件版本查看
- **版本信息展示**
  - 当前固件版本
  - 历史版本记录
  - 更新时间信息
  - OTA发布历史

### 五、IoT后台管理平台

#### 5.1 邮件工具管理
- **邮件创建发送**
  - 邮件模板管理
  - 批量邮件发送
  - 邮件状态跟踪

- **邮件工具列表**
  - 邮件记录查询
  - 发送状态管理
  - 邮件内容管理

#### 5.2 用户协议管理
- **协议内容管理**
  - Insight平台用户协议
  - 隐私协议管理
  - 多语言版本支持

#### 5.3 警报信息管理
- **警报配置**
  - 警报规则设置
  - 警报级别定义
  - 通知方式配置

- **警报信息处理**
  - 警报信息存储
  - 已读/未读管理
  - 警报删除功能

#### 5.4 OTA升级记录
- **升级记录管理**
  - 设备升级历史
  - 升级状态跟踪
  - 升级日志查看

#### 5.5 Protobuf数据处理
- **数据处理流程**
  - APP数据转发
  - 4G设备数据上报
  - 动态Protobuf解析
  - 数据模型管理

## 非功能性需求

### 性能要求
- 系统响应时间 < 2秒
- 支持并发用户数 > 1000
- 数据处理能力 > 10万条/分钟
- 系统可用性 > 99.9%

### 安全要求
- 数据传输加密
- 用户身份认证
- 权限访问控制
- 操作日志审计
- 数据备份恢复

### 兼容性要求
- 支持主流浏览器
- 移动端适配
- 多语言国际化
- 跨平台部署

## 项目交付物

### 软件交付物
- 完整源代码
- 可执行程序
- 数据库脚本
- 配置文件

### 文档交付物
- 需求规格说明书
- 系统设计文档
- 接口文档
- 用户操作手册
- 部署运维手册
- 测试报告

### 培训服务
- 系统管理员培训
- 最终用户培训
- 技术支持服务

## 项目工期安排

### 总体工期
项目总工期：**398.5人天**

### 分模块工期
- **系统设计**：12人天
- **Insight应用Web端**：169.5人天  
- **Insight应用Java后端**：105.5人天
- **IoT平台Web端**：9.5人天
- **IoT平台Java后端**：114人天

### 里程碑节点
1. 需求确认完成
2. 系统设计评审通过
3. 核心功能开发完成
4. 系统集成测试完成
5. 用户验收测试通过
6. 项目正式上线

## 验收标准

### 功能验收
- 所有功能模块正常运行
- 业务流程完整可用
- 数据处理准确无误
- 用户界面友好易用

### 性能验收
- 满足性能指标要求
- 压力测试通过
- 稳定性测试通过

### 安全验收
- 安全测试通过
- 漏洞扫描无高危问题
- 数据安全保护到位

## 技术支持与维护

### 质保期
- 免费质保期：12个月
- 7×24小时技术支持
- 远程技术支持服务

### 维护服务
- 系统监控服务
- 定期巡检服务
- 故障应急响应
- 版本升级服务

---

**注：本需求清单基于提供的IoT平台报价单V1.1制定，具体实施过程中可根据实际情况进行调整和完善。**
